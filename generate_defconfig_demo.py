#!/usr/bin/env python3
"""
演示如何生成和使用 defconfig
"""

import os
import subprocess
import sys

def create_demo_kconfig():
    """创建演示用的 Kconfig 文件"""
    
    kconfig_content = '''
mainmenu "Demo Project Configuration"

config FEATURE_A
    bool "Enable Feature A"
    default y
    help
      Enable feature A functionality.

config FEATURE_B
    bool "Enable Feature B"
    default n
    help
      Enable feature B functionality.

config DEBUG
    bool "Enable Debug Mode"
    default n
    help
      Enable debug output and checks.

config OPTIMIZATION
    bool "Enable Optimizations"
    default y
    help
      Enable compiler optimizations.

config LOG_LEVEL
    int "Log Level (0-3)"
    range 0 3
    default 1
    help
      Set logging level: 0=none, 1=error, 2=warning, 3=all

choice
    prompt "Target Platform"
    default PLATFORM_X86

config PLATFORM_X86
    bool "x86 Platform"

config PLATFORM_ARM
    bool "ARM Platform"

config PLATFORM_RISCV
    bool "RISC-V Platform"

endchoice

config CUSTOM_STRING
    string "Custom String Value"
    default "hello"
    help
      A custom string configuration.
'''
    
    with open('Kconfig', 'w') as f:
        f.write(kconfig_content)
    
    print("✓ 创建了演示 Kconfig 文件")

def demonstrate_defconfig_generation():
    """演示 defconfig 生成过程"""
    
    print("\n" + "="*60)
    print("defconfig 生成演示")
    print("="*60)
    
    # 方法1：从默认配置开始
    print("\n方法1：从默认配置生成 defconfig")
    print("1. 使用 alldefconfig.py 生成默认配置：")
    
    result = subprocess.run(['python', 'alldefconfig.py'], 
                          capture_output=True, text=True)
    if result.returncode == 0:
        print("   ✓ 生成了默认的 .config")
        
        # 显示生成的 .config
        if os.path.exists('.config'):
            print("\n   生成的 .config 内容：")
            with open('.config', 'r') as f:
                for i, line in enumerate(f, 1):
                    if i <= 10:  # 只显示前10行
                        print(f"     {line.rstrip()}")
                    elif i == 11:
                        print("     ... (更多内容)")
                        break
    
    print("\n2. 从 .config 生成 defconfig：")
    result = subprocess.run(['python', 'savedefconfig.py', '--out', 'defconfig'], 
                          capture_output=True, text=True)
    if result.returncode == 0:
        print("   ✓ 生成了 defconfig")
        
        if os.path.exists('defconfig'):
            print("\n   生成的 defconfig 内容：")
            with open('defconfig', 'r') as f:
                content = f.read().strip()
                if content:
                    for line in content.split('\n'):
                        print(f"     {line}")
                else:
                    print("     (空文件 - 所有配置都是默认值)")
    
    # 方法2：手动配置后生成
    print(f"\n方法2：手动配置后生成 defconfig")
    print("1. 手动创建自定义配置：")
    
    custom_config = '''CONFIG_FEATURE_A=y
CONFIG_FEATURE_B=y
CONFIG_DEBUG=y
CONFIG_LOG_LEVEL=3
# CONFIG_PLATFORM_X86 is not set
CONFIG_PLATFORM_ARM=y
# CONFIG_PLATFORM_RISCV is not set
CONFIG_CUSTOM_STRING="my_custom_value"
'''
    
    with open('.config', 'w') as f:
        f.write(custom_config)
    
    print("   ✓ 创建了自定义 .config")
    print("   内容：")
    for line in custom_config.strip().split('\n'):
        print(f"     {line}")
    
    print("\n2. 从自定义 .config 生成 defconfig：")
    result = subprocess.run(['python', 'savedefconfig.py', '--out', 'custom_defconfig'], 
                          capture_output=True, text=True)
    if result.returncode == 0:
        print("   ✓ 生成了 custom_defconfig")
        
        if os.path.exists('custom_defconfig'):
            print("\n   生成的 custom_defconfig 内容：")
            with open('custom_defconfig', 'r') as f:
                for line in f:
                    print(f"     {line.rstrip()}")

def demonstrate_defconfig_usage():
    """演示如何使用 defconfig"""
    
    print(f"\n" + "="*60)
    print("defconfig 使用演示")
    print("="*60)
    
    if not os.path.exists('custom_defconfig'):
        print("请先运行 defconfig 生成演示")
        return
    
    # 清理现有配置
    if os.path.exists('.config'):
        os.remove('.config')
    
    print("\n1. 从 defconfig 生成完整配置：")
    result = subprocess.run(['python', 'defconfig.py', 'custom_defconfig'], 
                          capture_output=True, text=True)
    if result.returncode == 0:
        print("   ✓ 从 custom_defconfig 生成了 .config")
        
        if os.path.exists('.config'):
            print("\n   生成的完整 .config：")
            with open('.config', 'r') as f:
                for line in f:
                    if line.strip():
                        print(f"     {line.rstrip()}")
    
    print("\n2. 生成头文件：")
    result = subprocess.run(['python', 'genconfig.py'], 
                          capture_output=True, text=True)
    if result.returncode == 0:
        print("   ✓ 生成了 autoconf.h")
        
        if os.path.exists('autoconf.h'):
            print("\n   生成的 autoconf.h 内容：")
            with open('autoconf.h', 'r') as f:
                for line in f:
                    if line.strip() and not line.startswith('/*'):
                        print(f"     {line.rstrip()}")

def show_workflow_examples():
    """显示工作流程示例"""
    
    print(f"\n" + "="*60)
    print("实际工作流程示例")
    print("="*60)
    
    workflows = [
        {
            "场景": "初次创建项目配置",
            "步骤": [
                "# 1. 创建默认配置",
                "python alldefconfig.py",
                "",
                "# 2. 使用 menuconfig 调整配置",
                "python menuconfig.py",
                "",
                "# 3. 保存为 defconfig",
                "python savedefconfig.py --out defconfig",
                "",
                "# 4. 提交到版本控制",
                "git add defconfig",
                "git commit -m 'Add initial configuration'"
            ]
        },
        {
            "场景": "修改现有配置",
            "步骤": [
                "# 1. 从 defconfig 生成当前配置",
                "python defconfig.py defconfig",
                "",
                "# 2. 使用 menuconfig 修改",
                "python menuconfig.py",
                "",
                "# 3. 保存新的 defconfig",
                "python savedefconfig.py --out defconfig",
                "",
                "# 4. 提交更改",
                "git add defconfig",
                "git commit -m 'Update configuration'"
            ]
        },
        {
            "场景": "新开发者设置环境",
            "步骤": [
                "# 1. 克隆项目",
                "git clone <project-url>",
                "cd <project>",
                "",
                "# 2. 生成配置文件",
                "python defconfig.py defconfig",
                "",
                "# 3. 生成头文件",
                "python genconfig.py",
                "",
                "# 4. 开始构建",
                "make"
            ]
        }
    ]
    
    for workflow in workflows:
        print(f"\n{workflow['场景']}:")
        for step in workflow['步骤']:
            if step.startswith('#'):
                print(f"  {step}")
            elif step:
                print(f"    {step}")
            else:
                print()

def cleanup():
    """清理演示文件"""
    files = ['Kconfig', '.config', 'autoconf.h', 'config.h', 
             'defconfig', 'custom_defconfig']
    
    print(f"\n清理演示文件...")
    for file in files:
        if os.path.exists(file):
            os.remove(file)
            print(f"  删除: {file}")

def main():
    """主函数"""
    print("defconfig 生成和使用指南")
    print("="*60)
    
    try:
        # 创建演示环境
        create_demo_kconfig()
        
        # 演示 defconfig 生成
        demonstrate_defconfig_generation()
        
        # 演示 defconfig 使用
        demonstrate_defconfig_usage()
        
        # 显示工作流程
        show_workflow_examples()
        
        # 询问是否清理
        print(f"\n是否清理演示文件? (y/N): ", end="")
        try:
            response = input().strip().lower()
            if response in ['y', 'yes']:
                cleanup()
            else:
                print("保留演示文件")
        except KeyboardInterrupt:
            print("\n保留演示文件")
            
    except Exception as e:
        print(f"错误: {e}")
        cleanup()

if __name__ == '__main__':
    main()
