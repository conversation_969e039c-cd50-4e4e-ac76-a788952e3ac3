# Configuration Management

This project uses a defconfig-based configuration system to avoid tracking generated files in git.

## Quick Start

```bash
# Use default configuration
python configure.py

# Use custom configuration
python configure.py --config my_custom_config

# Interactive configuration
python configure.py --menuconfig
```

## Files

- `defconfig` - Default minimal configuration (tracked in git)
- `.config` - Full configuration file (generated, not tracked)
- `autoconf.h` / `config.h` - Generated header files (not tracked)

## Why This Approach?

1. **Clean git history**: Only minimal configs are tracked
2. **No merge conflicts**: Generated files don't cause conflicts
3. **Platform independence**: Each developer generates their own build files
4. **Consistent defaults**: Everyone starts from the same base configuration

## Custom Configurations

To create a custom configuration:

1. Start with default: `python configure.py`
2. Modify `.config` manually or use menuconfig
3. Save minimal config: `python savedefconfig.py --out my_custom_defconfig`
4. Commit the minimal config file

## Build Integration

Add to your Makefile:

```makefile
.config: defconfig
	python configure.py

autoconf.h: .config
	python genconfig.py

build: autoconf.h
	# Your build commands here
```

## Environment Variables

- `KCONFIG_CONFIG`: Override default .config filename
- `KCONFIG_AUTOHEADER`: Override default autoconf.h path
