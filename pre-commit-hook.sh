#!/bin/bash
# Pre-commit hook to ensure configuration consistency
# Place this in .git/hooks/pre-commit and make it executable

# Check if .config is being committed
if git diff --cached --name-only | grep -q "^\.config$"; then
    echo "Warning: .config file is being committed"
    
    # Check if autoconf.h exists and is newer
    if [ -f autoconf.h ] && [ .config -ot autoconf.h ]; then
        echo "Error: autoconf.h is newer than .config"
        echo "This suggests .config was modified without regenerating autoconf.h"
        echo "Please run: python genconfig.py"
        exit 1
    fi
    
    # Suggest using defconfig instead
    echo "Consider using defconfig approach instead:"
    echo "1. python savedefconfig.py --out defconfig"
    echo "2. git add defconfig"
    echo "3. Add .config to .gitignore"
fi

# If defconfig exists, ensure it's up to date
if [ -f defconfig ] && [ -f .config ]; then
    if [ .config -nt defconfig ]; then
        echo "Warning: .config is newer than defconfig"
        echo "Consider updating defconfig: python savedefconfig.py --out defconfig"
    fi
fi
