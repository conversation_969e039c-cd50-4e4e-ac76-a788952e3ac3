/* 演示配置不一致导致的编译问题 */

#include "autoconf.h"

int main() {
    printf("编译时的配置状态:\n");
    
    #ifdef CONFIG_FEATURE_A
        printf("✓ FEATURE_A 已启用\n");
    #else
        printf("✗ FEATURE_A 未启用\n");
    #endif
    
    #ifdef CONFIG_DEBUG
        printf("✓ DEBUG 模式已启用\n");
        // 调试代码
        printf("调试信息: 详细日志输出\n");
    #else
        printf("✗ DEBUG 模式未启用\n");
        // 生产代码
    #endif
    
    #ifdef CONFIG_NEW
        printf("✓ 新功能已启用\n");
        // 新功能代码
        enable_new_feature();
    #else
        printf("✗ 新功能未启用\n");
        // 可能导致运行时错误，因为其他代码期望这个功能存在
    #endif
    
    return 0;
}

/* 
 * 问题分析：
 * 
 * 1. 开发者在 .config 中启用了 CONFIG_DEBUG=y
 *    但 autoconf.h 中仍然是 #undef CONFIG_DEBUG
 *    结果：编译时不包含调试代码，但开发者期望有调试输出
 * 
 * 2. 开发者添加了 CONFIG_NEW=y
 *    但 autoconf.h 中没有对应的 #define CONFIG_NEW
 *    结果：新功能代码不会被编译，可能导致链接错误或运行时错误
 * 
 * 3. 构建系统认为 autoconf.h 是最新的（基于时间戳）
 *    所以不会重新生成，问题会一直存在
 */
