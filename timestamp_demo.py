#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的时间戳问题演示
"""

import os
import time
from datetime import datetime

def format_time(timestamp):
    if timestamp:
        return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
    return "文件不存在"

def get_file_mtime(filepath):
    if os.path.exists(filepath):
        return os.path.getmtime(filepath)
    return None

def main():
    print("时间戳不一致问题演示")
    print("=" * 50)
    
    # 清理旧文件
    for f in ['.config', 'autoconf.h']:
        if os.path.exists(f):
            os.remove(f)
    
    # 1. 创建初始文件
    print("\n1. 创建初始文件:")
    with open('.config', 'w') as f:
        f.write("CONFIG_FEATURE_A=y\nCONFIG_DEBUG=n\n")
    
    time.sleep(1)  # 确保时间戳不同
    
    with open('autoconf.h', 'w') as f:
        f.write("#define CONFIG_FEATURE_A 1\n/* #undef CONFIG_DEBUG */\n")
    
    config_time = get_file_mtime('.config')
    autoconf_time = get_file_mtime('autoconf.h')
    
    print(f"  .config:    {format_time(config_time)}")
    print(f"  autoconf.h: {format_time(autoconf_time)}")
    print(f"  autoconf.h 比 .config 新: {autoconf_time > config_time}")
    
    # 2. 模拟开发者修改 .config
    print("\n2. 开发者A修改 .config:")
    time.sleep(2)
    with open('.config', 'w') as f:
        f.write("CONFIG_FEATURE_A=y\nCONFIG_DEBUG=y\nCONFIG_NEW=y\n")
    
    config_time = get_file_mtime('.config')
    autoconf_time = get_file_mtime('autoconf.h')
    
    print(f"  .config:    {format_time(config_time)} (已修改)")
    print(f"  autoconf.h: {format_time(autoconf_time)} (未更新)")
    print(f"  .config 比 autoconf.h 新: {config_time > autoconf_time}")
    
    # 3. 模拟 git checkout 的时间戳问题
    print("\n3. 模拟 git pull 后的情况:")
    print("   (git 会更新 .config 的时间戳，但 autoconf.h 保持不变)")
    
    # 模拟 git 将 .config 时间戳设置为较早的时间
    old_time = time.time() - 3600  # 1小时前
    os.utime('.config', (old_time, old_time))
    
    config_time = get_file_mtime('.config')
    autoconf_time = get_file_mtime('autoconf.h')
    
    print(f"  .config:    {format_time(config_time)} (git 设置的时间)")
    print(f"  autoconf.h: {format_time(autoconf_time)} (本地旧文件)")
    print(f"  autoconf.h 比 .config 新: {autoconf_time > config_time}")
    
    # 4. 显示内容不匹配
    print("\n4. 内容不匹配问题:")
    with open('.config', 'r') as f:
        config_content = f.read().strip()
    with open('autoconf.h', 'r') as f:
        autoconf_content = f.read().strip()
    
    print("  .config 内容:")
    for line in config_content.split('\n'):
        print(f"    {line}")
    
    print("  autoconf.h 内容:")
    for line in autoconf_content.split('\n'):
        print(f"    {line}")
    
    print("\n  问题:")
    print("    - .config 有 CONFIG_NEW=y，但 autoconf.h 中没有")
    print("    - .config 有 CONFIG_DEBUG=y，但 autoconf.h 中是注释掉的")
    print("    - 构建系统可能认为 autoconf.h 是最新的（时间戳更新）")
    print("    - 但实际内容已经过时！")
    
    # 5. Make 规则演示
    print("\n5. Make 构建规则的判断:")
    if autoconf_time > config_time:
        print("  Make 判断: autoconf.h 比 .config 新，无需重新生成")
        print("  结果: 使用过时的配置进行编译！")
    else:
        print("  Make 判断: .config 比 autoconf.h 新，需要重新生成")
        print("  结果: 正确，但需要开发者记得运行生成命令")

if __name__ == '__main__':
    main()
