#!/usr/bin/env python3
"""
defconfig.py 使用演示

这个脚本演示如何使用 defconfig.py 和相关工具进行配置管理
"""

import os
import subprocess
import sys

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n=== {description} ===")
    print(f"命令: {cmd}")
    print("输出:")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=".")
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("错误:", result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"执行失败: {e}")
        return False

def create_demo_files():
    """创建演示用的文件"""
    
    # 创建一个简单的 Kconfig 文件
    kconfig_content = '''
mainmenu "Demo Configuration"

config FEATURE_A
    bool "Enable Feature A"
    default y
    help
      This enables feature A in the system.

config FEATURE_B
    bool "Enable Feature B"
    default n
    help
      This enables feature B in the system.

config DEBUG
    bool "Enable Debug Mode"
    default n
    help
      Enable debug output and additional checks.

config OPTIMIZATION
    bool "Enable Optimizations"
    default y
    help
      Enable compiler optimizations.

config LOG_LEVEL
    int "Log Level (0-3)"
    range 0 3
    default 1
    help
      Set the logging level:
      0 = No logging
      1 = Errors only
      2 = Warnings and errors
      3 = All messages

choice
    prompt "Target Platform"
    default PLATFORM_X86

config PLATFORM_X86
    bool "x86 Platform"

config PLATFORM_ARM
    bool "ARM Platform"

config PLATFORM_RISCV
    bool "RISC-V Platform"

endchoice
'''
    
    with open('Kconfig', 'w') as f:
        f.write(kconfig_content)
    
    # 创建一个示例 defconfig 文件
    defconfig_content = '''# Minimal configuration for demo project
CONFIG_FEATURE_A=y
CONFIG_DEBUG=y
CONFIG_LOG_LEVEL=2
CONFIG_PLATFORM_ARM=y
'''
    
    with open('demo_defconfig', 'w') as f:
        f.write(defconfig_content)
    
    print("✓ 创建了演示文件:")
    print("  - Kconfig (配置定义文件)")
    print("  - demo_defconfig (最小配置文件)")

def demonstrate_defconfig_usage():
    """演示 defconfig.py 的使用"""
    
    print("\n" + "="*60)
    print("defconfig.py 使用演示")
    print("="*60)
    
    # 1. 使用 defconfig.py 生成完整配置
    success = run_command(
        "python defconfig.py demo_defconfig",
        "从 demo_defconfig 生成完整的 .config 文件"
    )
    
    if success and os.path.exists('.config'):
        print("\n生成的 .config 文件内容:")
        with open('.config', 'r') as f:
            content = f.read()
            for i, line in enumerate(content.split('\n')[:20], 1):  # 只显示前20行
                if line.strip():
                    print(f"  {i:2d}: {line}")
            if len(content.split('\n')) > 20:
                print("  ... (更多内容)")
    
    # 2. 演示配置的完整性
    print("\n=== 配置完整性检查 ===")
    if os.path.exists('.config'):
        with open('.config', 'r') as f:
            config_lines = f.readlines()
        
        print("检查配置项:")
        configs_to_check = [
            'CONFIG_FEATURE_A',
            'CONFIG_FEATURE_B', 
            'CONFIG_DEBUG',
            'CONFIG_OPTIMIZATION',
            'CONFIG_LOG_LEVEL',
            'CONFIG_PLATFORM_ARM'
        ]
        
        for config in configs_to_check:
            found = False
            for line in config_lines:
                if config in line:
                    print(f"  ✓ {line.strip()}")
                    found = True
                    break
            if not found:
                print(f"  ✗ {config} 未找到")
    
    # 3. 演示反向操作：从 .config 生成 defconfig
    run_command(
        "python savedefconfig.py --out generated_defconfig",
        "从 .config 生成最小配置文件"
    )
    
    if os.path.exists('generated_defconfig'):
        print("\n生成的最小配置文件内容:")
        with open('generated_defconfig', 'r') as f:
            for i, line in enumerate(f, 1):
                print(f"  {i:2d}: {line.rstrip()}")

def show_advanced_usage():
    """展示高级用法"""
    
    print("\n" + "="*60)
    print("高级用法演示")
    print("="*60)
    
    # 1. 指定不同的 Kconfig 文件
    print("\n1. 指定不同的 Kconfig 文件:")
    print("   python defconfig.py --kconfig MyKconfig my_defconfig")
    
    # 2. 使用环境变量指定输出文件
    print("\n2. 使用环境变量指定输出文件:")
    print("   KCONFIG_CONFIG=my_config.txt python defconfig.py demo_defconfig")
    
    # 3. 在 Makefile 中使用
    makefile_example = '''
# Makefile 示例
.config: defconfig
\tpython defconfig.py defconfig

config: .config
\t@echo "Configuration loaded from defconfig"

menuconfig: .config
\tpython menuconfig.py

savedefconfig:
\tpython savedefconfig.py --out defconfig
\t@echo "Minimal configuration saved to defconfig"
'''
    
    print("\n3. 在 Makefile 中集成:")
    print(makefile_example)
    
    # 4. 批处理多个配置
    print("\n4. 批处理多个配置:")
    batch_script = '''
#!/bin/bash
# 批处理脚本示例

configs=("debug_defconfig" "release_defconfig" "minimal_defconfig")

for config in "${configs[@]}"; do
    echo "Processing $config..."
    python defconfig.py "$config"
    python genconfig.py --header-path "autoconf_${config%.defconfig}.h"
done
'''
    print(batch_script)

def show_workflow():
    """展示完整的工作流程"""
    
    print("\n" + "="*60)
    print("推荐的配置管理工作流程")
    print("="*60)
    
    workflow_steps = [
        {
            "step": "1. 创建/修改配置",
            "commands": [
                "python menuconfig.py  # 交互式配置",
                "# 或者直接编辑 .config 文件"
            ]
        },
        {
            "step": "2. 保存最小配置",
            "commands": [
                "python savedefconfig.py --out defconfig",
                "git add defconfig"
            ]
        },
        {
            "step": "3. 提交到版本控制",
            "commands": [
                "git commit -m 'Update configuration'",
                "# 注意：不要提交 .config 和 autoconf.h"
            ]
        },
        {
            "step": "4. 其他开发者使用",
            "commands": [
                "git pull",
                "python defconfig.py defconfig  # 生成 .config",
                "python genconfig.py  # 生成 autoconf.h"
            ]
        },
        {
            "step": "5. 构建项目",
            "commands": [
                "make  # 或其他构建命令",
                "# 构建系统会使用 autoconf.h 中的配置"
            ]
        }
    ]
    
    for workflow in workflow_steps:
        print(f"\n{workflow['step']}:")
        for cmd in workflow['commands']:
            print(f"  {cmd}")

def cleanup():
    """清理演示文件"""
    files_to_remove = [
        'Kconfig', '.config', 'demo_defconfig', 
        'generated_defconfig', 'autoconf.h', 'config.h'
    ]
    
    print(f"\n清理演示文件...")
    for file in files_to_remove:
        if os.path.exists(file):
            os.remove(file)
            print(f"  删除: {file}")

def main():
    """主函数"""
    print("defconfig.py 使用指南和演示")
    print("="*60)
    
    # 创建演示文件
    create_demo_files()
    
    # 演示基本用法
    demonstrate_defconfig_usage()
    
    # 展示高级用法
    show_advanced_usage()
    
    # 展示工作流程
    show_workflow()
    
    # 询问是否清理
    print(f"\n是否清理演示文件? (y/N): ", end="")
    try:
        response = input().strip().lower()
        if response in ['y', 'yes']:
            cleanup()
        else:
            print("保留演示文件，你可以继续实验")
    except KeyboardInterrupt:
        print("\n保留演示文件")

if __name__ == '__main__':
    main()
