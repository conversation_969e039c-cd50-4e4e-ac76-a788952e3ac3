# 示例 Makefile：集成 Kconfiglib 配置管理
# 
# 这个 Makefile 演示了如何在项目中集成 defconfig.py 和相关工具

# 配置文件路径
KCONFIG_FILE ?= Kconfig
DEFCONFIG_FILE ?= defconfig
CONFIG_FILE ?= .config
AUTOCONF_H ?= autoconf.h

# Python 解释器
PYTHON ?= python

# 项目源文件（示例）
SOURCES = main.c utils.c
OBJECTS = $(SOURCES:.c=.o)
TARGET = myproject

# 默认目标
all: $(TARGET)

# 主要构建目标
$(TARGET): $(OBJECTS) $(AUTOCONF_H)
	@echo "Linking $(TARGET)..."
	gcc -o $@ $(OBJECTS)
	@echo "Build complete: $(TARGET)"

# 编译规则
%.o: %.c $(AUTOCONF_H)
	@echo "Compiling $<..."
	gcc -c -o $@ $< -include $(AUTOCONF_H)

# ============================================================================
# 配置管理目标
# ============================================================================

# 从 defconfig 生成 .config
$(CONFIG_FILE): $(DEFCONFIG_FILE)
	@echo "Generating configuration from $(DEFCONFIG_FILE)..."
	$(PYTHON) defconfig.py $(DEFCONFIG_FILE)
	@echo "Configuration loaded: $(CONFIG_FILE)"

# 从 .config 生成 autoconf.h
$(AUTOCONF_H): $(CONFIG_FILE)
	@echo "Generating header file from $(CONFIG_FILE)..."
	$(PYTHON) genconfig.py --header-path $(AUTOCONF_H)
	@echo "Header generated: $(AUTOCONF_H)"

# 配置相关的便捷目标
.PHONY: config menuconfig savedefconfig clean-config help-config

# 基本配置：从 defconfig 生成配置
config: $(CONFIG_FILE)
	@echo "Configuration ready"

# 交互式配置菜单
menuconfig: $(CONFIG_FILE)
	@echo "Starting menuconfig..."
	$(PYTHON) menuconfig.py $(KCONFIG_FILE)
	@echo "Configuration updated. Run 'make savedefconfig' to save changes."

# 保存当前配置为 defconfig
savedefconfig: $(CONFIG_FILE)
	@echo "Saving minimal configuration..."
	$(PYTHON) savedefconfig.py --kconfig $(KCONFIG_FILE) --out $(DEFCONFIG_FILE)
	@echo "Minimal configuration saved to $(DEFCONFIG_FILE)"
	@echo "Don't forget to commit: git add $(DEFCONFIG_FILE)"

# 强制重新生成配置
reconfig:
	@echo "Force regenerating configuration..."
	rm -f $(CONFIG_FILE) $(AUTOCONF_H)
	$(MAKE) config
	$(MAKE) $(AUTOCONF_H)

# ============================================================================
# 预定义配置目标
# ============================================================================

# 默认配置
defconfig:
	@echo "Loading default configuration..."
	$(PYTHON) alldefconfig.py
	$(PYTHON) genconfig.py --header-path $(AUTOCONF_H)
	@echo "Default configuration loaded"

# 调试配置
debug_defconfig:
	@if [ -f configs/debug_defconfig ]; then \
		echo "Loading debug configuration..."; \
		$(PYTHON) defconfig.py configs/debug_defconfig; \
		$(PYTHON) genconfig.py --header-path $(AUTOCONF_H); \
		echo "Debug configuration loaded"; \
	else \
		echo "Error: configs/debug_defconfig not found"; \
		exit 1; \
	fi

# 发布配置
release_defconfig:
	@if [ -f configs/release_defconfig ]; then \
		echo "Loading release configuration..."; \
		$(PYTHON) defconfig.py configs/release_defconfig; \
		$(PYTHON) genconfig.py --header-path $(AUTOCONF_H); \
		echo "Release configuration loaded"; \
	else \
		echo "Error: configs/release_defconfig not found"; \
		exit 1; \
	fi

# 最小配置
minimal_defconfig:
	@echo "Loading minimal configuration..."
	$(PYTHON) allnoconfig.py
	$(PYTHON) genconfig.py --header-path $(AUTOCONF_H)
	@echo "Minimal configuration loaded"

# ============================================================================
# 清理目标
# ============================================================================

.PHONY: clean clean-all clean-config

# 清理构建文件
clean:
	@echo "Cleaning build files..."
	rm -f $(OBJECTS) $(TARGET)

# 清理配置文件
clean-config:
	@echo "Cleaning configuration files..."
	rm -f $(CONFIG_FILE) $(AUTOCONF_H) config.h .config.old

# 完全清理
clean-all: clean clean-config
	@echo "Complete cleanup done"

# ============================================================================
# 验证和检查目标
# ============================================================================

.PHONY: check-config list-config validate-config

# 检查配置状态
check-config:
	@echo "Configuration status:"
	@if [ -f $(DEFCONFIG_FILE) ]; then \
		echo "  ✓ $(DEFCONFIG_FILE) exists"; \
	else \
		echo "  ✗ $(DEFCONFIG_FILE) missing"; \
	fi
	@if [ -f $(CONFIG_FILE) ]; then \
		echo "  ✓ $(CONFIG_FILE) exists"; \
		if [ $(DEFCONFIG_FILE) -nt $(CONFIG_FILE) ]; then \
			echo "  ⚠ $(DEFCONFIG_FILE) is newer than $(CONFIG_FILE)"; \
			echo "    Run 'make config' to update"; \
		fi; \
	else \
		echo "  ✗ $(CONFIG_FILE) missing"; \
	fi
	@if [ -f $(AUTOCONF_H) ]; then \
		echo "  ✓ $(AUTOCONF_H) exists"; \
		if [ $(CONFIG_FILE) -nt $(AUTOCONF_H) ]; then \
			echo "  ⚠ $(CONFIG_FILE) is newer than $(AUTOCONF_H)"; \
			echo "    Header will be regenerated automatically"; \
		fi; \
	else \
		echo "  ✗ $(AUTOCONF_H) missing"; \
	fi

# 列出当前配置
list-config: $(CONFIG_FILE)
	@echo "Current configuration:"
	@grep "^CONFIG_" $(CONFIG_FILE) | head -20
	@if [ $$(grep -c "^CONFIG_" $(CONFIG_FILE)) -gt 20 ]; then \
		echo "... (and $$(( $$(grep -c "^CONFIG_" $(CONFIG_FILE)) - 20 )) more)"; \
	fi

# 验证配置完整性
validate-config: $(CONFIG_FILE)
	@echo "Validating configuration..."
	@$(PYTHON) -c "import kconfiglib; kconf = kconfiglib.Kconfig('$(KCONFIG_FILE)'); kconf.load_config('$(CONFIG_FILE)'); print('✓ Configuration is valid')" 2>/dev/null || echo "✗ Configuration validation failed"

# ============================================================================
# 帮助目标
# ============================================================================

.PHONY: help help-config

# 主帮助
help:
	@echo "Available targets:"
	@echo "  all              - Build the project"
	@echo "  clean            - Clean build files"
	@echo "  clean-all        - Clean build and config files"
	@echo ""
	@echo "Configuration targets:"
	@echo "  config           - Generate .config from defconfig"
	@echo "  menuconfig       - Interactive configuration menu"
	@echo "  savedefconfig    - Save current config as defconfig"
	@echo "  reconfig         - Force regenerate configuration"
	@echo ""
	@echo "Predefined configurations:"
	@echo "  defconfig        - Load default configuration"
	@echo "  debug_defconfig  - Load debug configuration"
	@echo "  release_defconfig- Load release configuration"
	@echo "  minimal_defconfig- Load minimal configuration"
	@echo ""
	@echo "Utilities:"
	@echo "  check-config     - Check configuration status"
	@echo "  list-config      - List current configuration"
	@echo "  validate-config  - Validate configuration"
	@echo "  help-config      - Detailed configuration help"

# 配置帮助
help-config:
	@echo "Configuration Management Help"
	@echo "============================"
	@echo ""
	@echo "Basic workflow:"
	@echo "  1. make config           # Load configuration from defconfig"
	@echo "  2. make menuconfig       # Modify configuration (optional)"
	@echo "  3. make                  # Build project"
	@echo "  4. make savedefconfig    # Save changes (if any)"
	@echo ""
	@echo "Files:"
	@echo "  $(DEFCONFIG_FILE)        - Minimal configuration (tracked in git)"
	@echo "  $(CONFIG_FILE)           - Full configuration (generated)"
	@echo "  $(AUTOCONF_H)            - C header file (generated)"
	@echo ""
	@echo "Environment variables:"
	@echo "  KCONFIG_FILE     - Kconfig file path (default: $(KCONFIG_FILE))"
	@echo "  DEFCONFIG_FILE   - defconfig file path (default: $(DEFCONFIG_FILE))"
	@echo "  PYTHON           - Python interpreter (default: $(PYTHON))"
	@echo ""
	@echo "For new projects:"
	@echo "  1. make defconfig        # Start with defaults"
	@echo "  2. make menuconfig       # Customize"
	@echo "  3. make savedefconfig    # Save as defconfig"
	@echo "  4. git add defconfig     # Commit to version control"

# ============================================================================
# 特殊规则
# ============================================================================

# 确保配置文件存在
$(OBJECTS): | $(AUTOCONF_H)

# 防止删除中间文件
.PRECIOUS: $(CONFIG_FILE) $(AUTOCONF_H)

# 声明 phony 目标
.PHONY: all clean clean-all config menuconfig savedefconfig reconfig
.PHONY: defconfig debug_defconfig release_defconfig minimal_defconfig
.PHONY: check-config list-config validate-config help help-config
