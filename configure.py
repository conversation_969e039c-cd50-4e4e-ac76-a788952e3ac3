#!/usr/bin/env python3

"""
Configuration setup script for the project.

This script helps set up the build configuration by:
1. Loading the default configuration (defconfig)
2. Generating the autoconf.h header file
3. Providing options for custom configurations

Usage:
    python configure.py                    # Use default config
    python configure.py --config myconfig  # Use custom config file
    python configure.py --menuconfig       # Interactive configuration
"""

import argparse
import os
import sys
import subprocess

def main():
    parser = argparse.ArgumentParser(description=__doc__, 
                                   formatter_class=argparse.RawDescriptionHelpFormatter)
    
    parser.add_argument('--config', 
                       help='Custom configuration file to use (default: defconfig)')
    
    parser.add_argument('--menuconfig', action='store_true',
                       help='Run interactive menuconfig')
    
    parser.add_argument('--output', default='.config',
                       help='Output configuration file (default: .config)')
    
    args = parser.parse_args()
    
    if args.menuconfig:
        print("Running menuconfig...")
        try:
            subprocess.run([sys.executable, 'menuconfig.py'], check=True)
        except subprocess.CalledProcessError:
            print("Error: menuconfig failed")
            return 1
        except FileNotFoundError:
            print("Error: menuconfig.py not found")
            return 1
    else:
        # Use defconfig or custom config
        config_file = args.config or 'defconfig'
        
        if not os.path.exists(config_file):
            print(f"Error: Configuration file '{config_file}' not found")
            return 1
        
        print(f"Loading configuration from {config_file}...")
        
        # Copy config file to .config
        try:
            with open(config_file, 'r') as src, open(args.output, 'w') as dst:
                dst.write(src.read())
            print(f"Configuration saved to {args.output}")
        except IOError as e:
            print(f"Error copying configuration: {e}")
            return 1
    
    # Generate autoconf.h
    print("Generating autoconf.h...")
    try:
        subprocess.run([sys.executable, 'genconfig.py'], check=True)
        print("autoconf.h generated successfully")
    except subprocess.CalledProcessError:
        print("Warning: Failed to generate autoconf.h")
    except FileNotFoundError:
        print("Warning: genconfig.py not found")
    
    print("Configuration complete!")
    return 0

if __name__ == '__main__':
    sys.exit(main())
