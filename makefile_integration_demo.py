#!/usr/bin/env python3
"""
演示如何在 Makefile 中集成 defconfig.py
"""

import os
import subprocess
import sys

def create_demo_project():
    """创建演示项目"""
    
    # 创建 Kconfig
    kconfig_content = '''
mainmenu "Demo Project"

config FEATURE_DEBUG
    bool "Enable Debug"
    default n

config FEATURE_LOGGING
    bool "Enable Logging"
    default y

config LOG_LEVEL
    int "Log Level"
    range 1 5
    default 3
    depends on FEATURE_LOGGING
'''
    
    with open('Kconfig', 'w') as f:
        f.write(kconfig_content)
    
    # 创建示例 C 代码
    main_c_content = '''#include <stdio.h>
#include "autoconf.h"

int main() {
    printf("Demo Project\\n");
    
#ifdef CONFIG_FEATURE_DEBUG
    printf("Debug mode enabled\\n");
#endif

#ifdef CONFIG_FEATURE_LOGGING
    printf("Logging enabled, level: %d\\n", CONFIG_LOG_LEVEL);
#else
    printf("Logging disabled\\n");
#endif

    return 0;
}
'''
    
    with open('main.c', 'w') as f:
        f.write(main_c_content)
    
    # 创建初始 defconfig
    defconfig_content = '''CONFIG_FEATURE_DEBUG=y
CONFIG_LOG_LEVEL=5
'''
    
    with open('defconfig', 'w') as f:
        f.write(defconfig_content)
    
    # 创建简化的 Makefile
    makefile_content = '''# Demo Project Makefile

PYTHON ?= python
TARGET = demo
SOURCES = main.c

all: $(TARGET)

$(TARGET): main.o autoconf.h
\tgcc -o $@ main.o

main.o: main.c autoconf.h
\tgcc -c main.c -include autoconf.h

.config: defconfig
\t@echo "Generating .config from defconfig..."
\t$(PYTHON) defconfig.py defconfig

autoconf.h: .config
\t@echo "Generating autoconf.h..."
\t$(PYTHON) genconfig.py

config: .config
\t@echo "Configuration ready"

menuconfig: .config
\t$(PYTHON) menuconfig.py

savedefconfig: .config
\t$(PYTHON) savedefconfig.py --out defconfig
\t@echo "defconfig updated"

clean:
\trm -f *.o $(TARGET) .config autoconf.h config.h

run: $(TARGET)
\t./$(TARGET)

.PHONY: all config menuconfig savedefconfig clean run
'''
    
    with open('Makefile', 'w') as f:
        f.write(makefile_content)
    
    print("✓ 创建了演示项目文件:")
    print("  - Kconfig (配置定义)")
    print("  - main.c (示例程序)")
    print("  - defconfig (默认配置)")
    print("  - Makefile (构建脚本)")

def demonstrate_makefile_usage():
    """演示 Makefile 的使用"""
    
    print("\n" + "="*60)
    print("Makefile 集成演示")
    print("="*60)
    
    commands = [
        {
            "desc": "1. 生成配置文件",
            "cmd": "make config",
            "explain": "从 defconfig 生成 .config 和 autoconf.h"
        },
        {
            "desc": "2. 构建项目",
            "cmd": "make",
            "explain": "编译并链接程序"
        },
        {
            "desc": "3. 运行程序",
            "cmd": "make run",
            "explain": "运行编译好的程序"
        }
    ]
    
    for item in commands:
        print(f"\n{item['desc']}: {item['explain']}")
        print(f"命令: {item['cmd']}")
        
        result = subprocess.run(item['cmd'].split(), 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("输出:")
            if result.stdout:
                for line in result.stdout.split('\n'):
                    if line.strip():
                        print(f"  {line}")
        else:
            print("错误:")
            if result.stderr:
                for line in result.stderr.split('\n'):
                    if line.strip():
                        print(f"  {line}")

def show_advanced_makefile_features():
    """展示高级 Makefile 功能"""
    
    print(f"\n" + "="*60)
    print("高级 Makefile 功能")
    print("="*60)
    
    features = [
        {
            "功能": "自动依赖管理",
            "代码": '''# 自动依赖规则
autoconf.h: .config
\t$(PYTHON) genconfig.py

.config: defconfig
\t$(PYTHON) defconfig.py defconfig

# 确保源文件依赖配置
%.o: %.c autoconf.h
\tgcc -c $< -include autoconf.h'''
        },
        {
            "功能": "多配置支持",
            "代码": '''# 预定义配置
debug_config:
\t$(PYTHON) defconfig.py configs/debug_defconfig
\t$(PYTHON) genconfig.py

release_config:
\t$(PYTHON) defconfig.py configs/release_defconfig
\t$(PYTHON) genconfig.py

# 配置验证
check-config:
\t@if [ .config -ot defconfig ]; then \\
\t\techo "Warning: defconfig is newer"; \\
\tfi'''
        },
        {
            "功能": "清理和重置",
            "代码": '''# 分层清理
clean-build:
\trm -f *.o $(TARGET)

clean-config:
\trm -f .config autoconf.h

clean-all: clean-build clean-config

# 强制重新配置
reconfig:
\trm -f .config autoconf.h
\t$(MAKE) config'''
        },
        {
            "功能": "配置检查",
            "代码": '''# 配置状态检查
status:
\t@echo "Configuration status:"
\t@ls -la defconfig .config autoconf.h 2>/dev/null || true

# 配置差异
diff-config:
\t@if [ -f .config.old ]; then \\
\t\tdiff -u .config.old .config || true; \\
\telse \\
\t\techo "No previous config found"; \\
\tfi'''
        }
    ]
    
    for feature in features:
        print(f"\n{feature['功能']}:")
        print(feature['代码'])

def show_best_practices():
    """显示最佳实践"""
    
    print(f"\n" + "="*60)
    print("Makefile 集成最佳实践")
    print("="*60)
    
    practices = [
        {
            "实践": "1. 正确的依赖关系",
            "说明": [
                "• 确保 autoconf.h 依赖于 .config",
                "• 确保 .config 依赖于 defconfig",
                "• 所有源文件都依赖于 autoconf.h"
            ],
            "示例": '''autoconf.h: .config
.config: defconfig
%.o: %.c autoconf.h'''
        },
        {
            "实践": "2. 使用 .PHONY 目标",
            "说明": [
                "• 配置相关的目标应该是 phony",
                "• 避免与同名文件冲突"
            ],
            "示例": '''.PHONY: config menuconfig savedefconfig clean'''
        },
        {
            "实践": "3. 提供便捷目标",
            "说明": [
                "• config: 生成配置",
                "• menuconfig: 交互式配置",
                "• savedefconfig: 保存配置"
            ],
            "示例": '''config: .config
menuconfig: .config
\t$(PYTHON) menuconfig.py'''
        },
        {
            "实践": "4. 错误处理",
            "说明": [
                "• 检查必要文件是否存在",
                "• 提供有用的错误信息"
            ],
            "示例": '''config:
\t@if [ ! -f defconfig ]; then \\
\t\techo "Error: defconfig not found"; \\
\t\texit 1; \\
\tfi
\t$(PYTHON) defconfig.py defconfig'''
        }
    ]
    
    for practice in practices:
        print(f"\n{practice['实践']}")
        for point in practice['说明']:
            print(f"  {point}")
        if '示例' in practice:
            print("  示例:")
            for line in practice['示例'].split('\n'):
                if line.strip():
                    print(f"    {line}")

def cleanup():
    """清理演示文件"""
    files = ['Kconfig', 'main.c', 'defconfig', 'Makefile', 
             '.config', 'autoconf.h', 'config.h', 'main.o', 'demo']
    
    print(f"\n清理演示文件...")
    for file in files:
        if os.path.exists(file):
            os.remove(file)
            print(f"  删除: {file}")

def main():
    """主函数"""
    print("Makefile 集成 defconfig.py 演示")
    print("="*60)
    
    try:
        # 创建演示项目
        create_demo_project()
        
        # 演示 Makefile 使用
        demonstrate_makefile_usage()
        
        # 展示高级功能
        show_advanced_makefile_features()
        
        # 显示最佳实践
        show_best_practices()
        
        # 询问是否清理
        print(f"\n是否清理演示文件? (y/N): ", end="")
        try:
            response = input().strip().lower()
            if response in ['y', 'yes']:
                cleanup()
            else:
                print("保留演示文件，你可以继续实验")
        except KeyboardInterrupt:
            print("\n保留演示文件")
            
    except Exception as e:
        print(f"错误: {e}")
        cleanup()

if __name__ == '__main__':
    main()
