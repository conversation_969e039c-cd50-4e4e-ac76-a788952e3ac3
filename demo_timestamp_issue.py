#!/usr/bin/env python3
"""
演示时间戳不一致导致的构建系统问题

这个脚本模拟了当 .config 被 git 追踪而 autoconf.h 不被追踪时可能出现的问题
"""

import os
import time
import subprocess
import tempfile
from datetime import datetime

def get_file_mtime(filepath):
    """获取文件修改时间"""
    if os.path.exists(filepath):
        return os.path.getmtime(filepath)
    return None

def format_time(timestamp):
    """格式化时间戳"""
    if timestamp:
        return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
    return "文件不存在"

def create_demo_files():
    """创建演示文件"""
    
    # 创建一个简单的 .config 文件
    config_content = """# Demo configuration
CONFIG_FEATURE_A=y
CONFIG_FEATURE_B=n
CONFIG_DEBUG=y
"""
    
    with open('.config', 'w') as f:
        f.write(config_content)
    
    # 创建一个简单的 autoconf.h 文件
    autoconf_content = """/* Generated configuration header */
#define CONFIG_FEATURE_A 1
#undef CONFIG_FEATURE_B
#define CONFIG_DEBUG 1
"""
    
    with open('autoconf.h', 'w') as f:
        f.write(autoconf_content)
    
    print("✓ 创建了演示文件 .config 和 autoconf.h")

def simulate_git_operations():
    """模拟 git 操作导致的时间戳问题"""
    
    print("\n" + "="*60)
    print("场景演示：Git 操作导致的时间戳不一致问题")
    print("="*60)
    
    # 场景1：初始状态
    print("\n1. 初始状态：")
    config_time = get_file_mtime('.config')
    autoconf_time = get_file_mtime('autoconf.h')
    
    print(f"   .config     修改时间: {format_time(config_time)}")
    print(f"   autoconf.h  修改时间: {format_time(autoconf_time)}")
    
    if config_time and autoconf_time:
        if autoconf_time > config_time:
            print("   ✓ autoconf.h 比 .config 新，构建系统认为无需重新生成")
        else:
            print("   ⚠ autoconf.h 比 .config 旧，构建系统会重新生成")
    
    # 等待一秒确保时间戳不同
    time.sleep(1)
    
    # 场景2：修改 .config
    print("\n2. 开发者A修改了 .config：")
    config_content_new = """# Demo configuration - MODIFIED
CONFIG_FEATURE_A=y
CONFIG_FEATURE_B=y  # 改为 y
CONFIG_DEBUG=n      # 改为 n
CONFIG_NEW_FEATURE=y  # 新增配置
"""
    
    with open('.config', 'w') as f:
        f.write(config_content_new)
    
    config_time = get_file_mtime('.config')
    autoconf_time = get_file_mtime('autoconf.h')
    
    print(f"   .config     修改时间: {format_time(config_time)}")
    print(f"   autoconf.h  修改时间: {format_time(autoconf_time)}")
    print("   ✓ 开发者A提交了 .config 到 git")
    
    # 场景3：模拟 git checkout/pull 操作
    print("\n3. 开发者B执行 git pull：")
    
    # 模拟 git 操作：.config 的时间戳会变成 checkout 的时间
    # 但 autoconf.h 保持不变（因为它不在 git 中）
    
    # 先保存 autoconf.h 的时间戳
    old_autoconf_time = autoconf_time
    
    # 模拟 git checkout 更新 .config 的时间戳
    # 在实际情况中，git checkout 会将文件时间戳设置为 checkout 时间
    time.sleep(1)
    os.utime('.config')  # 更新 .config 的时间戳到当前时间
    
    config_time = get_file_mtime('.config')
    autoconf_time = get_file_mtime('autoconf.h')
    
    print(f"   .config     修改时间: {format_time(config_time)} (git 更新)")
    print(f"   autoconf.h  修改时间: {format_time(autoconf_time)} (本地旧文件)")
    
    print("\n   问题分析：")
    if autoconf_time < config_time:
        print("   ❌ autoconf.h 比 .config 旧！")
        print("   ❌ 但 autoconf.h 的内容可能与新的 .config 不匹配")
        print("   ❌ 构建系统会认为需要重新生成 autoconf.h")
        print("   ❌ 这是正确的，但如果开发者忘记重新生成...")
    else:
        print("   ⚠ autoconf.h 比 .config 新或相等")
        print("   ⚠ 构建系统认为无需重新生成")
        print("   ❌ 但实际上内容已经不匹配了！")

def simulate_makefile_behavior():
    """模拟 Makefile 的行为"""
    
    print("\n" + "="*60)
    print("Makefile 构建系统的判断逻辑演示")
    print("="*60)
    
    # 创建一个简单的 Makefile 规则演示
    makefile_content = """
# 典型的 Makefile 规则
autoconf.h: .config
\t@echo "检查时间戳..."
\t@if [ .config -nt autoconf.h ]; then \\
\t\techo "❌ .config 比 autoconf.h 新，需要重新生成"; \\
\t\techo "正在生成 autoconf.h..."; \\
\t\tpython genconfig.py; \\
\telse \\
\t\techo "✓ autoconf.h 是最新的，无需重新生成"; \\
\tfi

.PHONY: check-timestamps
check-timestamps:
\t@echo "文件时间戳检查："
\t@echo "  .config:    $$(stat -c '%Y %n' .config 2>/dev/null || echo '文件不存在')"
\t@echo "  autoconf.h: $$(stat -c '%Y %n' autoconf.h 2>/dev/null || echo '文件不存在')"
\t@if [ -f .config ] && [ -f autoconf.h ]; then \\
\t\tif [ .config -nt autoconf.h ]; then \\
\t\t\techo "  结果: .config 更新"; \\
\t\telse \\
\t\t\techo "  结果: autoconf.h 是最新的"; \\
\t\tfi; \\
\tfi
"""
    
    with open('Makefile.demo', 'w') as f:
        f.write(makefile_content)
    
    print("创建了演示 Makefile.demo")
    print("\n可以运行以下命令测试：")
    print("  make -f Makefile.demo check-timestamps")
    print("  make -f Makefile.demo autoconf.h")

def demonstrate_content_mismatch():
    """演示内容不匹配的问题"""
    
    print("\n" + "="*60)
    print("内容不匹配问题演示")
    print("="*60)
    
    # 读取当前 .config
    with open('.config', 'r') as f:
        config_content = f.read()
    
    # 读取当前 autoconf.h
    with open('autoconf.h', 'r') as f:
        autoconf_content = f.read()
    
    print("\n当前 .config 内容：")
    print("-" * 30)
    print(config_content)
    
    print("当前 autoconf.h 内容：")
    print("-" * 30)
    print(autoconf_content)
    
    print("问题分析：")
    print("❌ .config 中有 CONFIG_NEW_FEATURE=y")
    print("❌ 但 autoconf.h 中没有对应的 #define CONFIG_NEW_FEATURE")
    print("❌ .config 中 CONFIG_FEATURE_B=y，但 autoconf.h 中是 #undef CONFIG_FEATURE_B")
    print("❌ 这会导致编译时使用错误的配置！")

def show_real_world_scenarios():
    """展示真实世界的场景"""
    
    print("\n" + "="*60)
    print("真实世界的问题场景")
    print("="*60)
    
    scenarios = [
        {
            "title": "场景1：CI/CD 构建失败",
            "description": [
                "• 开发者提交了新的 .config",
                "• CI 系统 checkout 代码",
                "• CI 系统没有 autoconf.h（因为不在 git 中）",
                "• 构建系统生成 autoconf.h，但可能与开发者本地不同",
                "• 导致构建结果不一致或测试失败"
            ]
        },
        {
            "title": "场景2：团队协作问题",
            "description": [
                "• 开发者A修改 .config 并提交",
                "• 开发者B拉取代码",
                "• 开发者B的本地 autoconf.h 没有更新",
                "• 开发者B编译时使用了旧的配置",
                "• 导致功能异常或调试困难"
            ]
        },
        {
            "title": "场景3：增量构建错误",
            "description": [
                "• .config 被 git 更新，时间戳变新",
                "• autoconf.h 存在但内容过时",
                "• Make 判断需要重新生成 autoconf.h",
                "• 但如果生成脚本有问题或环境不同",
                "• 可能生成错误的 autoconf.h"
            ]
        },
        {
            "title": "场景4：跨平台构建问题",
            "description": [
                "• Windows 和 Linux 的文件系统时间戳精度不同",
                "• Git 在不同平台的时间戳处理不同",
                "• 可能导致构建系统判断错误",
                "• 特别是在使用网络文件系统时"
            ]
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{scenario['title']}:")
        for desc in scenario['description']:
            print(f"  {desc}")

def main():
    """主函数"""
    print("时间戳不一致问题演示")
    print("=" * 60)
    
    # 清理旧文件
    for f in ['.config', 'autoconf.h', 'Makefile.demo']:
        if os.path.exists(f):
            os.remove(f)
    
    # 创建演示文件
    create_demo_files()
    
    # 演示各种场景
    simulate_git_operations()
    demonstrate_content_mismatch()
    simulate_makefile_behavior()
    show_real_world_scenarios()
    
    print("\n" + "="*60)
    print("解决方案总结")
    print("="*60)
    print("1. 使用 defconfig 模式：只追踪最小配置文件")
    print("2. 如果必须追踪 .config：")
    print("   • 同时追踪 autoconf.h，或")
    print("   • 在构建脚本中总是重新生成 autoconf.h")
    print("   • 使用 .gitattributes 控制时间戳行为")
    print("3. 建立清晰的构建依赖关系")
    print("4. 在 CI/CD 中总是从头生成配置文件")

if __name__ == '__main__':
    main()
