# 简化版 Makefile - 基本的 defconfig 集成

# 配置文件
DEFCONFIG ?= defconfig
CONFIG_FILE ?= .config
AUTOCONF_H ?= autoconf.h

# Python 命令
PYTHON ?= python

# 项目文件
SOURCES = main.c
TARGET = myapp

# 默认目标
all: $(TARGET)

# 主程序
$(TARGET): main.o $(AUTOCONF_H)
	gcc -o $@ main.o

main.o: main.c $(AUTOCONF_H)
	gcc -c main.c -include $(AUTOCONF_H)

# 配置管理
$(CONFIG_FILE): $(DEFCONFIG)
	$(PYTHON) defconfig.py $(DEFCONFIG)

$(AUTOCONF_H): $(CONFIG_FILE)
	$(PYTHON) genconfig.py

# 便捷目标
config: $(CONFIG_FILE)

menuconfig:
	$(PYTHON) menuconfig.py
	@echo "Run 'make savedefconfig' to save changes"

savedefconfig:
	$(PYTHON) savedefconfig.py --out $(DEFCONFIG)

clean:
	rm -f *.o $(TARGET) $(CONFIG_FILE) $(AUTOCONF_H)

.PHONY: all config menuconfig savedefconfig clean
