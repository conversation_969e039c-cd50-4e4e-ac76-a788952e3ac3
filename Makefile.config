# Configuration management for projects that track .config
# This Makefile helps manage the relationship between .config and autoconf.h

# Default configuration file
CONFIG_FILE ?= .config
AUTOCONF_H ?= autoconf.h

# Phony targets
.PHONY: config clean-config sync-config check-config

# Generate autoconf.h from .config
$(AUTOCONF_H): $(CONFIG_FILE)
	@echo "Generating $(AUTOCONF_H) from $(CONFIG_FILE)..."
	python genconfig.py --header-path $(AUTOCONF_H)
	@echo "$(AUTOCONF_H) updated"

# Force regeneration of autoconf.h
config: 
	@echo "Force regenerating configuration files..."
	python genconfig.py --header-path $(AUTOCONF_H)

# Clean generated files (but keep .config)
clean-config:
	@echo "Cleaning generated configuration files..."
	rm -f $(AUTOCONF_H) config.h .config.old

# Sync: ensure autoconf.h is up to date with .config
sync-config: $(AUTOCONF_H)
	@echo "Configuration files are in sync"

# Check if autoconf.h needs updating
check-config:
	@if [ $(CONFIG_FILE) -nt $(AUTOCONF_H) ]; then \
		echo "WARNING: $(AUTOCONF_H) is older than $(CONFIG_FILE)"; \
		echo "Run 'make sync-config' to update"; \
	else \
		echo "Configuration files are up to date"; \
	fi

# Help target
help-config:
	@echo "Configuration targets:"
	@echo "  config       - Force regenerate autoconf.h"
	@echo "  sync-config  - Ensure autoconf.h is up to date"
	@echo "  check-config - Check if files need updating"
	@echo "  clean-config - Remove generated files"
