#!/bin/bash
# 演示时间戳不一致导致的构建问题

echo "=== 构建系统时间戳问题演示 ==="
echo ""

# 检查当前文件状态
echo "1. 当前文件状态："
ls -la .config autoconf.h 2>/dev/null || echo "文件不存在"
echo ""

# 显示时间戳比较结果
echo "2. Make 构建系统的判断："
if [ -f .config ] && [ -f autoconf.h ]; then
    if [ .config -nt autoconf.h ]; then
        echo "   ✓ .config 比 autoconf.h 新 -> 会重新生成 autoconf.h"
        NEED_REBUILD=true
    else
        echo "   ❌ autoconf.h 比 .config 新或相等 -> 跳过重新生成"
        echo "   ❌ 但内容可能已经不匹配！"
        NEED_REBUILD=false
    fi
else
    echo "   文件缺失，无法比较"
    exit 1
fi
echo ""

# 检查内容一致性
echo "3. 内容一致性检查："
echo "   .config 中的配置："
grep "CONFIG_" .config | while read line; do
    echo "     $line"
done

echo ""
echo "   autoconf.h 中的定义："
grep -E "(#define|#undef)" autoconf.h | while read line; do
    echo "     $line"
done

echo ""
echo "4. 发现的不一致："

# 检查 CONFIG_DEBUG
if grep -q "CONFIG_DEBUG=y" .config; then
    if grep -q "#define CONFIG_DEBUG" autoconf.h; then
        echo "   ✓ CONFIG_DEBUG: 一致"
    else
        echo "   ❌ CONFIG_DEBUG: .config=y 但 autoconf.h 未定义"
    fi
fi

# 检查 CONFIG_NEW
if grep -q "CONFIG_NEW=y" .config; then
    if grep -q "#define CONFIG_NEW" autoconf.h; then
        echo "   ✓ CONFIG_NEW: 一致"
    else
        echo "   ❌ CONFIG_NEW: .config=y 但 autoconf.h 中缺失"
    fi
fi

echo ""
echo "5. 构建结果预测："
if [ "$NEED_REBUILD" = "false" ]; then
    echo "   ❌ Make 不会重新生成 autoconf.h"
    echo "   ❌ 编译将使用过时的配置"
    echo "   ❌ 可能导致："
    echo "      - 功能缺失（CONFIG_NEW 未定义）"
    echo "      - 调试信息缺失（CONFIG_DEBUG 未定义）"
    echo "      - 运行时错误或异常行为"
else
    echo "   ✓ Make 会重新生成 autoconf.h"
    echo "   ✓ 配置将保持一致"
fi

echo ""
echo "6. 解决方案："
echo "   方案1: 强制重新生成"
echo "     make clean && make"
echo ""
echo "   方案2: 使用 defconfig 模式"
echo "     - 只追踪 defconfig 文件"
echo "     - .config 和 autoconf.h 都不追踪"
echo ""
echo "   方案3: 修复构建规则"
echo "     - 总是检查内容而不仅仅是时间戳"
echo "     - 或者总是重新生成 autoconf.h"
